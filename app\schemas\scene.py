from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class SceneSpeechRequest(BaseModel):
    """场景发言请求"""
    
    elid: int = Field(..., description="练习情况ID")
    cid: int = Field(..., description="角色ID")
    played: int = Field(..., description="是否是学员扮演（0：否；1：是）")
    content: str = Field(..., description="发言内容")
    to_cids: Optional[str] = Field("", description="@列表（角色ID列表，用逗号分隔，可为空字符串）")

    class Config:
        from_attributes = True


class SceneSpeechCreateResponse(BaseModel):
    """场景发言创建响应"""
    
    id: int = Field(..., description="发言ID")
    status: int = Field(..., description="发言状态")
    elid: int = Field(..., description="练习情况ID")

    class Config:
        from_attributes = True


class SceneCharacterResponse(BaseModel):
    """场景角色响应"""

    id: int = Field(..., description="角色ID")
    name: str = Field(..., description="姓名")
    gender: int = Field(..., description="性别（0：未知；1：男；2：女）")
    avatar: Optional[str] = Field(None, description="头像URL")
    profile: str = Field(..., description="人物资料")
    timbre_type: int = Field(..., description="音色类型（0：未设置；1：火山引擎）")
    timbre: Optional[str] = Field(None, description="音色")
    played: int = Field(..., description="是否是学员扮演（0：否；1：是）")

    class Config:
        from_attributes = True


class SceneGuideResponse(BaseModel):
    """场景指南响应"""

    title: str = Field(..., description="指南标题")
    details: str = Field(..., description="指南详情")

    class Config:
        from_attributes = True


class SceneSpeechResponse(BaseModel):
    """场景发言响应"""

    id: int = Field(..., description="发言ID")
    cid: int = Field(..., description="角色ID")
    played: int = Field(..., description="是否是学员扮演（0：否；1：是）")
    content: str = Field(..., description="发言内容")
    to_cids: Optional[str] = Field(None, description="@列表（角色ID列表，用逗号分隔，可为空字符串）")
    status: int = Field(..., description="发言状态（0：未处理；1：处理中；2：已处理）")
    ctime: datetime = Field(..., description="发言时间")

    class Config:
        from_attributes = True


class SceneBasicResponse(BaseModel):
    """场景基本信息响应"""

    title: str = Field(..., description="标题")
    pic: Optional[str] = Field(None, description="图片URL")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="时长（分钟）")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    report: Optional[str] = Field(None, description="整体点评报告URL")
    btime: Optional[datetime] = Field(None, description="开始练习时间")
    stime: Optional[datetime] = Field(None, description="提交练习时间")
    utime: Optional[datetime] = Field(None, description="上次练习时间")
    status: int = Field(..., description="练习状态（0：待练习；1：练习中；2：已提交）")
    eid: int = Field(..., description="练习ID")
    elid: Optional[int] = Field(None, description="练习情况ID")
    comments: Optional[str] = Field(None, description="AI整体点评")
    tid: Optional[int] = Field(None, description="老师ID")
    tname: Optional[str] = Field(None, description="老师姓名")
    tavatar: Optional[str] = Field(None, description="老师头像URL")
    characters: List[SceneCharacterResponse] = Field(default_factory=list, description="角色列表")
    guides: List[SceneGuideResponse] = Field(default_factory=list, description="指南列表")
    speeches: List[SceneSpeechResponse] = Field(default_factory=list, description="场景练习情况（发言）列表")

    class Config:
        from_attributes = True


class SceneSpeechJobRequest(BaseModel):
    """场景发言任务请求"""
    
    elid: int = Field(..., description="练习情况ID")

    class Config:
        from_attributes = True


class SceneSpeechJobResponse(BaseModel):
    """场景发言任务响应"""
    
    id: int = Field(..., description="任务ID")
    cid: int = Field(..., description="完成任务的角色ID")
    status: int = Field(..., description="任务状态")
    sync: int = Field(..., description="同步或异步（0:异步；1:同步；）")
    ctime: datetime = Field(..., description="发言时间")

    class Config:
        from_attributes = True


class SceneSpeechJobsResponse(BaseModel):
    """场景发言任务列表响应"""
    
    jobs: List[SceneSpeechJobResponse] = Field(default_factory=list, description="任务列表")

    class Config:
        from_attributes = True


class SceneSpeechBatchDeleteRequest(BaseModel):
    """批量删除对话请求"""
    
    from_id: int = Field(..., description="对话ID")
    elid: int = Field(..., description="练习情况ID")

    class Config:
        from_attributes = True


class SceneSpeechBatchDeleteResponse(BaseModel):
    """批量删除对话响应"""
    
    deleted_count: int = Field(..., description="删除对话的数量")

    class Config:
        from_attributes = True