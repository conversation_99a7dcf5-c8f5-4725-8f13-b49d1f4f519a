import json
import logging
from datetime import datetime
from typing import Optional

from sqlalchemy import and_
from sqlalchemy.orm import Session

from app.core.constants import BOT_KEY_ZHCOL, BOT_KEY_ZHSPK
from app.core.exceptions import (
    AIServiceException,
    DatabaseException,
    ResourceNotFoundException,
    SpeechJobInvalidStatusException,
    SpeechJobNotFoundException,
)
from app.models.models import (
    TntCharacter,
    TntClassExercise,
    TntCue,
    TntExercise,
    TntExerciseLog,
    TntLine,
    TntScene,
    TntSceneCharacter,
    TntSceneGuide,
    TntSceneSpeech,
    TntSpeechJob,
    TntStudent,
    TntTeacher,
)
from app.schemas.scene import (
    SceneBasicResponse,
    SceneCharacterResponse,
    SceneGuideResponse,
    SceneSpeechCreateResponse,
    SceneSpeechJobRequest,
    SceneSpeechJobResponse,
    SceneSpeechJobsResponse,
    SceneSpeechRequest,
    SceneSpeechResponse,
)
from app.services.ai import get_fastgpt_json_response, get_fastgpt_stream_response
from app.services.bot import get_bot_config
from app.utils.oss import get_oss_url

logger = logging.getLogger(__name__)


def get_scene_basic_info(
    db: Session, scene_id: int, class_id: int, current_user: TntStudent
) -> Optional[SceneBasicResponse]:
    """
    获取场景基本信息

    Args:
        db: 数据库会话
        scene_id: 场景ID
        class_id: 班级ID
        current_user: 当前用户

    Returns:
        场景基本信息或None
    """
    # 创建预过滤的子查询
    scene_filtered = (
        db.query(TntScene)
        .filter(
            and_(
                TntScene.id == scene_id,
                TntScene.tenant_id == current_user.tenant_id,
            )
        )
        .subquery("scene_filtered")
    )

    exercise_filtered = (
        db.query(TntExercise)
        .filter(
            and_(
                TntExercise.tenant_id == current_user.tenant_id,
                TntExercise.active == 1,
            )
        )
        .subquery("exercise_filtered")
    )

    exercise_log_filtered = (
        db.query(TntExerciseLog)
        .filter(
            and_(
                TntExerciseLog.cid == class_id,
                TntExerciseLog.sid == current_user.id,
                TntExerciseLog.tenant_id == current_user.tenant_id,
                TntExerciseLog.active == 1,
            )
        )
        .subquery("exercise_log_filtered")
    )

    class_exercise_filtered = (
        db.query(TntClassExercise)
        .filter(
            and_(
                TntClassExercise.cid == class_id,
                TntClassExercise.tenant_id == current_user.tenant_id,
            )
        )
        .subquery("class_exercise_filtered")
    )

    teacher_filtered = (
        db.query(TntTeacher)
        .filter(TntTeacher.tenant_id == current_user.tenant_id)
        .subquery("teacher_filtered")
    )

    # 使用子查询进行连接，选择具体的列
    scene_data = (
        db.query(
            scene_filtered.c.id.label("scene_id"),
            exercise_filtered.c.id.label("exercise_id"),
            exercise_filtered.c.title.label("exercise_title"),
            exercise_filtered.c.pic.label("exercise_pic"),
            exercise_filtered.c.intro.label("exercise_intro"),
            exercise_filtered.c.duration.label("exercise_duration"),
            exercise_filtered.c.bgtext.label("exercise_bgtext"),
            exercise_filtered.c.bgvideo.label("exercise_bgvideo"),
            exercise_log_filtered.c.id.label("log_id"),
            exercise_log_filtered.c.comments.label("log_comments"),
            exercise_log_filtered.c.report.label("log_report"),
            exercise_log_filtered.c.btime.label("log_btime"),
            exercise_log_filtered.c.stime.label("log_stime"),
            exercise_log_filtered.c.utime.label("log_utime"),
            exercise_log_filtered.c.status.label("log_status"),
            teacher_filtered.c.id.label("teacher_id"),
            teacher_filtered.c.name.label("teacher_name"),
            teacher_filtered.c.avatar.label("teacher_avatar"),
        )
        .select_from(scene_filtered)
        .join(
            exercise_filtered,
            exercise_filtered.c.id == scene_filtered.c.eid,
        )
        .outerjoin(
            exercise_log_filtered,
            exercise_log_filtered.c.eid == exercise_filtered.c.id,
        )
        .outerjoin(
            class_exercise_filtered,
            class_exercise_filtered.c.eid == exercise_filtered.c.id,
        )
        .outerjoin(
            teacher_filtered,
            teacher_filtered.c.id == class_exercise_filtered.c.tid,
        )
        .first()
    )

    if not scene_data:
        return None

    # 获取场景角色列表
    characters_data = (
        db.query(TntCharacter, TntSceneCharacter.played)
        .join(
            TntSceneCharacter,
            and_(
                TntSceneCharacter.cid == TntCharacter.id,
                TntSceneCharacter.sid == scene_id,
                TntSceneCharacter.tenant_id == current_user.tenant_id,
            ),
        )
        .filter(
            and_(
                TntCharacter.tenant_id == current_user.tenant_id,
                TntCharacter.active == 1,
            )
        )
        .order_by(TntSceneCharacter.priority)
        .all()
    )

    characters = [
        SceneCharacterResponse(
            id=character.id,
            name=character.name,
            gender=character.gender,
            avatar=get_oss_url(character.avatar) if character.avatar else None,
            profile=character.profile,
            timbre_type=character.timbre_type,
            timbre=character.timbre,
            played=played,
        )
        for character, played in characters_data
    ]

    # 获取场景指南列表
    guides_data = (
        db.query(TntSceneGuide)
        .filter(
            and_(
                TntSceneGuide.sid == scene_id,
                TntSceneGuide.tenant_id == current_user.tenant_id,
            )
        )
        .order_by(TntSceneGuide.priority)
        .all()
    )

    guides = [
        SceneGuideResponse(
            title=guide.title,
            details=guide.details,
        )
        for guide in guides_data
    ]

    # 获取场景发言列表（如果有练习情况ID）
    speeches = []
    if scene_data.log_id:
        speeches_data = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.elid == scene_data.log_id,
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                )
            )
            .order_by(TntSceneSpeech.ctime)
            .all()
        )

        speeches = [
            SceneSpeechResponse(
                id=speech.id,
                cid=speech.cid,
                played=speech.played,
                content=speech.content,
                to_cids=speech.to_cids or "",
                status=speech.status,
                ctime=speech.ctime,
            )
            for speech in speeches_data
        ]

    # 构建响应
    response = SceneBasicResponse(
        title=scene_data.exercise_title,
        pic=get_oss_url(scene_data.exercise_pic) if scene_data.exercise_pic else None,
        intro=scene_data.exercise_intro,
        duration=scene_data.exercise_duration,
        bgtext=scene_data.exercise_bgtext,
        bgvideo=get_oss_url(scene_data.exercise_bgvideo)
        if scene_data.exercise_bgvideo
        else None,
        report=get_oss_url(scene_data.log_report) if scene_data.log_report else None,
        btime=scene_data.log_btime,
        stime=scene_data.log_stime,
        utime=scene_data.log_utime,
        status=scene_data.log_status if scene_data.log_status is not None else 0,
        eid=scene_data.exercise_id,
        elid=scene_data.log_id,
        comments=scene_data.log_comments,
        tid=scene_data.teacher_id,
        tname=scene_data.teacher_name,
        tavatar=get_oss_url(scene_data.teacher_avatar)
        if scene_data.teacher_avatar
        else None,
        characters=characters,
        guides=guides,
        speeches=speeches,
    )

    return response


def create_scene_speech(
    db: Session, speech_data: SceneSpeechRequest, current_user: TntStudent
) -> Optional[SceneSpeechCreateResponse]:
    """
    创建场景发言

    Args:
        db: 数据库会话
        speech_data: 发言数据
        current_user: 当前用户

    Returns:
        创建的发言信息或None

    Raises:
        ResourceNotFoundException: 练习情况或角色不存在
        DatabaseException: 数据库操作失败
    """
    # 在事务外进行验证查询，避免不必要的锁定
    try:
        # 验证练习情况是否存在且属于当前用户
        exercise_log = (
            db.query(TntExerciseLog)
            .filter(
                and_(
                    TntExerciseLog.id == speech_data.elid,
                    TntExerciseLog.sid == current_user.id,
                    TntExerciseLog.tenant_id == current_user.tenant_id,
                    TntExerciseLog.active == 1,
                )
            )
            .first()
        )

        if not exercise_log:
            logger.warning(
                f"Exercise log not found or no permission: elid={speech_data.elid}, user_id={current_user.id}"
            )
            raise ResourceNotFoundException("练习情况", str(speech_data.elid))

        # 验证角色是否存在且属于当前租户
        character = (
            db.query(TntCharacter)
            .filter(
                and_(
                    TntCharacter.id == speech_data.cid,
                    TntCharacter.tenant_id == current_user.tenant_id,
                    TntCharacter.active == 1,
                )
            )
            .first()
        )

        if not character:
            logger.warning(
                f"Character not found: cid={speech_data.cid}, tenant_id={current_user.tenant_id}"
            )
            raise ResourceNotFoundException("角色", str(speech_data.cid))

    except ResourceNotFoundException:
        # 重新抛出业务异常
        raise
    except Exception as e:
        logger.error(f"Error validating data for scene speech: {e}")
        raise DatabaseException("验证数据", str(e))

    # 在单一事务中创建发言记录
    try:
        # 创建发言记录
        new_speech = TntSceneSpeech(
            tenant_id=current_user.tenant_id,
            elid=speech_data.elid,
            cid=speech_data.cid,
            played=speech_data.played,
            content=speech_data.content,
            to_cids=speech_data.to_cids or "",
            status=0,  # 默认状态为0（未处理）
        )

        db.add(new_speech)
        db.commit()
        db.refresh(new_speech)

        # 新增：更新elid对应的TntExerciseLog的utime为当前时间
        exercise_log = db.query(TntExerciseLog).filter(
            and_(
                TntExerciseLog.id == speech_data.elid,
                TntExerciseLog.tenant_id == current_user.tenant_id,
            )
        ).first()
        if exercise_log:
            exercise_log.utime = datetime.now()
            db.commit()

        return SceneSpeechCreateResponse(
            id=new_speech.id,
            status=new_speech.status,
            elid=new_speech.elid,
        )

    except Exception as e:
        logger.error(f"Error creating scene speech: {e}")
        db.rollback()
        raise DatabaseException("创建发言记录", str(e))


def _reset_speech_status(db: Session, speech_id: int, tenant_id: int) -> None:
    """
    重置speech状态为0，使用独立事务

    Args:
        db: 数据库会话
        speech_id: 发言ID
        tenant_id: 租户ID
    """
    try:
        speech_to_reset = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.id == speech_id,
                    TntSceneSpeech.tenant_id == tenant_id,
                )
            )
            .first()
        )
        if speech_to_reset and speech_to_reset.status == 1:
            speech_to_reset.status = 0
            db.commit()
    except Exception as reset_error:
        logger.error(f"Error resetting speech status: {reset_error}")
        db.rollback()


async def create_scene_speech_jobs(
    db: Session,
    speech_id: int,
    job_data: SceneSpeechJobRequest,
    current_user: TntStudent,
) -> SceneSpeechJobsResponse:
    """
    创建场景发言任务

    Args:
        db: 数据库会话
        speech_id: 发言ID
        job_data: 任务数据
        current_user: 当前用户

    Returns:
        创建的任务列表

    Raises:
        ResourceNotFoundException: 发言不存在或不满足处理条件
        DatabaseException: 数据库操作失败
        AIServiceException: AI服务调用失败
    """

    # Step 1: 幂等性检查 - 先检查是否已经有对应的jobs
    existing_jobs = (
        db.query(TntSpeechJob)
        .filter(
            and_(
                TntSpeechJob.elid == job_data.elid,
                TntSpeechJob.tenant_id == current_user.tenant_id,
            )
        )
        .all()
    )

    if existing_jobs:
        # 如果已经存在jobs，直接返回现有结果（幂等性）
        jobs_response = [
            SceneSpeechJobResponse(
                id=job.id,
                cid=job.cid,
                status=job.status,
                sync=job.sync,
                ctime=job.ctime,
            )
            for job in existing_jobs
        ]
        return SceneSpeechJobsResponse(jobs=jobs_response)

    # Step 2: 使用乐观锁 + 悲观锁组合策略
    speech = None
    try:
        # 首先使用乐观锁检查
        speech_check = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.id == speech_id,
                    TntSceneSpeech.elid == job_data.elid,
                    TntSceneSpeech.played == 1,
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not speech_check:
            raise ResourceNotFoundException(
                "发言记录", f"speech_id={speech_id}, elid={job_data.elid}"
            )

        if speech_check.status != 0:
            # 如果状态不是0，说明正在被处理或已处理
            if speech_check.status == 2:
                # 状态为2表示已完成，检查是否有对应的jobs
                existing_jobs_retry = (
                    db.query(TntSpeechJob)
                    .filter(
                        and_(
                            TntSpeechJob.elid == job_data.elid,
                            TntSpeechJob.tenant_id == current_user.tenant_id,
                        )
                    )
                    .all()
                )
                if existing_jobs_retry:
                    jobs_response = [
                        SceneSpeechJobResponse(
                            id=job.id,
                            cid=job.cid,
                            status=job.status,
                            sync=job.sync,
                            ctime=job.ctime,
                        )
                        for job in existing_jobs_retry
                    ]
                    return SceneSpeechJobsResponse(jobs=jobs_response)

            raise ResourceNotFoundException(
                "发言记录", f"speech_id={speech_id} 状态不正确: {speech_check.status}"
            )

        # 使用悲观锁获取并更新状态
        speech = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.id == speech_id,
                    TntSceneSpeech.elid == job_data.elid,
                    TntSceneSpeech.played == 1,
                    TntSceneSpeech.status == 0,
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                )
            )
            .with_for_update()  # 悲观锁，防止并发
            .first()
        )

        if not speech:
            # 在悲观锁期间状态可能被其他事务修改
            raise ResourceNotFoundException(
                "发言记录", f"speech_id={speech_id} 状态已被修改"
            )

        # 原子性更新状态为1（分布式锁）
        speech.status = 1
        db.commit()

    except ResourceNotFoundException:
        # 重新抛出业务异常
        raise
    except Exception as e:
        logger.error(f"Error acquiring speech lock: {e}")
        db.rollback()
        raise DatabaseException("获取发言锁", str(e))

    # Step 3: 数据准备阶段 - 使用统一的异常处理
    try:
        # 获取会议背景（bgtext字段）
        exercise_log = (
            db.query(TntExerciseLog)
            .filter(
                and_(
                    TntExerciseLog.id == job_data.elid,
                    TntExerciseLog.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not exercise_log:
            raise ResourceNotFoundException("练习日志", str(job_data.elid))

        # 通过exercise_log获取exercise和scene信息
        exercise = (
            db.query(TntExercise)
            .filter(
                and_(
                    TntExercise.id == exercise_log.eid,
                    TntExercise.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not exercise:
            raise ResourceNotFoundException("练习", str(exercise_log.eid))

        scene = (
            db.query(TntScene)
            .filter(
                and_(
                    TntScene.eid == exercise.id,
                    TntScene.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not scene:
            raise ResourceNotFoundException("场景", str(exercise.id))

        background = exercise.bgtext or ""
        rules = scene.pv_scripts or ""

        # 获取会议记录（根据elid查询tnt_scene_speech记录）
        speeches_data = (
            db.query(TntSceneSpeech, TntCharacter.name)
            .join(
                TntCharacter,
                and_(
                    TntCharacter.id == TntSceneSpeech.cid,
                    TntCharacter.tenant_id == current_user.tenant_id,
                ),
            )
            .filter(
                and_(
                    TntSceneSpeech.elid == job_data.elid,
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                )
            )
            .order_by(TntSceneSpeech.id)
            .all()
        )

        summary_lines = []
        for speech_record, character_name in speeches_data:
            summary_lines.append(
                f"{speech_record.ctime.strftime('%Y-%m-%d %H:%M:%S')} {character_name}: {speech_record.content}"
            )
        summary = "\n".join(summary_lines)

        # 获取参会人员（通过scene_character关联）
        scene_characters = (
            db.query(TntCharacter)
            .join(
                TntSceneCharacter,
                and_(
                    TntSceneCharacter.cid == TntCharacter.id,
                    TntSceneCharacter.sid == scene.id,
                    TntSceneCharacter.tenant_id == current_user.tenant_id,
                ),
            )
            .filter(
                and_(
                    TntCharacter.tenant_id == current_user.tenant_id,
                    TntCharacter.active == 1,
                )
            )
            .all()
        )

        persons_lines = []
        for character in scene_characters:
            persons_lines.append(
                f"{character.name}: {character.pv_profile or character.profile}"
            )
        persons = "\n".join(persons_lines)

        # 获取发言者信息
        speaker_character = (
            db.query(TntCharacter)
            .filter(
                and_(
                    TntCharacter.id == speech.cid,
                    TntCharacter.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not speaker_character:
            raise ResourceNotFoundException("发言者角色", str(speech.cid))

        speaker = speaker_character.name

        variables = {
            "background": background,
            "summary": summary,
            "persons": persons,
            "rules": rules,
            "speaker": speaker,
        }

        # 获取机器人配置
        bot_config = get_bot_config(db, current_user.tenant_id, BOT_KEY_ZHCOL)
        if not bot_config:
            raise ResourceNotFoundException("机器人配置", BOT_KEY_ZHCOL)

        # Step 3: 调用AI服务（不在事务中）
        ai_response = await get_fastgpt_json_response(
            messages=[speech.content],
            api_endpoint=bot_config["api_endpoint"],
            api_key=bot_config["api_key"],
            variables=variables,
        )

        # 解析AI响应
        try:
            ai_result = json.loads(ai_response)
            if not isinstance(ai_result, list):
                raise AIServiceException("AI响应格式错误：期望列表格式")
        except json.JSONDecodeError:
            raise AIServiceException("AI响应不是有效的JSON格式")

        # Step 4: 解析AI返回的角色和line_id信息
        character_line_mapping = {}  # {character_name: line_id or None}

        for item in ai_result:
            if isinstance(item, dict):
                for character_name, line_id in item.items():
                    try:
                        # 尝试转换为int
                        character_line_mapping[character_name] = int(line_id)
                    except (ValueError, TypeError):
                        # 如果不能转换为int，记录为None，表示没有有效的line_id
                        character_line_mapping[character_name] = None

        if not character_line_mapping:
            raise AIServiceException("AI响应中未找到角色信息")

        # 获取有效的line_ids
        valid_line_ids = [
            line_id
            for line_id in character_line_mapping.values()
            if line_id is not None
        ]

        # 查询有效的lines数据（按priority排序）
        lines_data = []
        if valid_line_ids:
            lines_data = (
                db.query(TntLine, TntCue.serial)
                .join(
                    TntCue,
                    and_(
                        TntCue.id == TntLine.cueid,
                        TntCue.tenant_id == current_user.tenant_id,
                    ),
                )
                .filter(
                    and_(
                        TntLine.id.in_(valid_line_ids),
                        TntLine.tenant_id == current_user.tenant_id,
                        TntLine.active == 1,
                    )
                )
                .order_by(TntLine.priority)  # 按priority从小到大排序
                .all()
            )

        # 处理to_cids过滤
        to_cids_list = []
        if speech.to_cids:
            try:
                to_cids_list = [
                    int(cid.strip()) for cid in speech.to_cids.split(",") if cid.strip()
                ]
            except ValueError:
                to_cids_list = []

        # Step 5: 准备job数据（不在事务中）
        jobs_to_create = []

        # 创建角色ID到line信息的映射
        cid_to_line_info = {}  # {cid: (line, serial)}
        for line, serial in lines_data:
            cid_to_line_info[line.cid] = (line, serial)

        # 创建角色名称到角色ID的映射（用于AI返回的角色信息）
        ai_character_cids = set()  # AI返回的角色ID集合
        for character_name, line_id in character_line_mapping.items():
            for sc in scene_characters:
                if sc.name == character_name:
                    ai_character_cids.add(sc.id)
                    break

        processed_character_ids = set()

        # 根据to_cids_list是否为空来决定处理顺序
        if to_cids_list:
            # 如果to_cids_list不为空，按照to_cids_list的顺序来构造jobs_to_create
            for cid in to_cids_list:
                # 获取角色信息
                character = (
                    db.query(TntCharacter)
                    .filter(
                        and_(
                            TntCharacter.id == cid,
                            TntCharacter.tenant_id == current_user.tenant_id,
                        )
                    )
                    .first()
                )

                if not character:
                    logger.warning(f"Character not found: cid={cid}")
                    continue

                # 记录已处理的角色ID
                processed_character_ids.add(character.id)

                # 检查是否是AI返回的角色，如果不是，vars字段为null
                if cid not in ai_character_cids:
                    # 不是AI返回的角色，vars字段为null
                    jobs_to_create.append(
                        {
                            "tenant_id": current_user.tenant_id,
                            "elid": job_data.elid,
                            "sid": speech_id,
                            "cid": cid,
                            "status": 0,
                            "vars": None,  # vars字段为null
                            "sync": 1,
                        }
                    )
                    continue

                # 是AI返回的角色，检查是否有对应的line信息
                if cid in cid_to_line_info:
                    # 有line信息，构建完整的vars
                    line, serial = cid_to_line_info[cid]

                    # 构建others（所有参会人员，去掉当前角色）
                    others_lines = []
                    for sc in scene_characters:
                        if sc.id != character.id:
                            others_lines.append(f"{sc.name}: {sc.pv_profile or sc.profile}")
                    others = "\n".join(others_lines)

                    # 构建vars字典
                    vars_dict = {
                        "profile": character.pv_profile or character.profile,
                        "others": others,
                        "background": background,
                        "summary": summary,
                        "topic_abilities": line.pv_ability,
                        "abilities": character.pv_ability or "",
                        "topic": line.pv_topic,
                        "topic_fewshots": "",  # 根据需求，这个字段暂时为空
                        "topic_restrictions": line.pv_restriction,
                        "restrictions": character.pv_restriction or "",
                        "speaker": speaker,
                    }

                    jobs_to_create.append(
                        {
                            "tenant_id": current_user.tenant_id,
                            "elid": job_data.elid,
                            "sid": speech_id,
                            "cid": cid,
                            "status": 0,
                            "vars": json.dumps(vars_dict, ensure_ascii=False),
                            "sync": serial,
                        }
                    )
                else:
                    # 没有line信息，vars字段为空字符串
                    jobs_to_create.append(
                        {
                            "tenant_id": current_user.tenant_id,
                            "elid": job_data.elid,
                            "sid": speech_id,
                            "cid": cid,
                            "status": 0,
                            "vars": "",  # 没有vars
                            "sync": 1,  # 没有serial，使用1
                        }
                    )
        else:
            # 如果to_cids_list为空，按照AI返回的角色和line_id信息的顺序来构造jobs_to_create

            # 首先处理有line_id的角色（已按priority排序）
            for line, serial in lines_data:
                # 获取角色信息
                character = (
                    db.query(TntCharacter)
                    .filter(
                        and_(
                            TntCharacter.id == line.cid,
                            TntCharacter.tenant_id == current_user.tenant_id,
                        )
                    )
                    .first()
                )

                if not character:
                    logger.warning(f"Character not found: cid={line.cid}")
                    continue

                # 记录已处理的角色ID
                processed_character_ids.add(character.id)

                # 构建others（所有参会人员，去掉当前角色）
                others_lines = []
                for sc in scene_characters:
                    if sc.id != character.id:
                        others_lines.append(f"{sc.name}: {sc.pv_profile or sc.profile}")
                others = "\n".join(others_lines)

                # 构建vars字典
                vars_dict = {
                    "profile": character.pv_profile or character.profile,
                    "others": others,
                    "background": background,
                    "summary": summary,
                    "topic_abilities": line.pv_ability,
                    "abilities": character.pv_ability or "",
                    "topic": line.pv_topic,
                    "topic_fewshots": "",  # 根据需求，这个字段暂时为空
                    "topic_restrictions": line.pv_restriction,
                    "restrictions": character.pv_restriction or "",
                    "speaker": speaker,
                }

                jobs_to_create.append(
                    {
                        "tenant_id": current_user.tenant_id,
                        "elid": job_data.elid,
                        "sid": speech_id,
                        "cid": line.cid,
                        "status": 0,
                        "vars": json.dumps(vars_dict, ensure_ascii=False),
                        "sync": serial,
                    }
                )

            # 然后处理没有line_id的角色（最后创建）
            for character_name, line_id in character_line_mapping.items():
                if line_id is None:  # 没有有效的line_id
                    # 根据角色名称查找角色
                    character = None
                    for sc in scene_characters:
                        if sc.name == character_name:
                            character = sc
                            break

                    if not character:
                        logger.warning(f"Character not found by name: {character_name}")
                        continue

                    # 检查是否已经处理过这个角色
                    if character.id in processed_character_ids:
                        continue

                    # 记录已处理的角色ID
                    processed_character_ids.add(character.id)

                    # 为没有line_id的角色创建job，不包含vars字段
                    jobs_to_create.append(
                        {
                            "tenant_id": current_user.tenant_id,
                            "elid": job_data.elid,
                            "sid": speech_id,
                            "cid": character.id,
                            "status": 0,
                            "vars": "",  # 没有vars
                            "sync": 0,  # 没有serial，使用0
                        }
                    )

        if not jobs_to_create:
            raise AIServiceException("未能生成有效的任务")

    except Exception as e:
        # 统一的异常处理：恢复状态并重新抛出
        logger.error(f"Error in data preparation phase: {e}")
        _reset_speech_status(db, speech_id, current_user.tenant_id)

        # 根据异常类型重新抛出
        if isinstance(e, (ResourceNotFoundException, AIServiceException)):
            raise e
        else:
            raise DatabaseException("准备任务数据", str(e))

    # Step 6: 批量创建job记录和更新speech状态（单一事务）
    try:
        # 开启事务进行批量写操作
        created_jobs = []
        for job_data_dict in jobs_to_create:
            new_job = TntSpeechJob(**job_data_dict)
            db.add(new_job)
            created_jobs.append(new_job)

        # 刷新以获取ID
        db.flush()

        # 使用乐观锁验证speech状态
        speech_to_complete = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.id == speech_id,
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not speech_to_complete or speech_to_complete.status != 1:
            raise DatabaseException("发言状态不一致", "可能存在并发修改")

        speech_to_complete.status = 2

        # 提交所有更改
        db.commit()

        # 构造返回结果
        jobs_response = [
            SceneSpeechJobResponse(
                id=job.id,
                cid=job.cid,
                status=job.status,
                sync=job.sync,
                ctime=job.ctime,
            )
            for job in created_jobs
        ]

        return SceneSpeechJobsResponse(jobs=jobs_response)

    except Exception as e:
        # 写操作失败，回滚事务并恢复状态
        logger.error(f"Error creating jobs: {e}")
        db.rollback()
        _reset_speech_status(db, speech_id, current_user.tenant_id)
        raise DatabaseException("创建任务", str(e))


def get_scene_speech_jobs(
    db: Session, speech_id: int, elid: int, current_user: TntStudent
) -> Optional[SceneSpeechJobsResponse]:
    """
    获取场景发言任务列表

    Args:
        db: 数据库会话
        speech_id: 发言ID
        elid: 练习情况ID
        current_user: 当前用户

    Returns:
        SceneSpeechJobsResponse: 任务列表响应，如果没有找到记录则返回None
    """
    try:
        # Step 1: 在tnt_scene_speech中根据id，elid条件以及played=1且status=0获取记录
        speech = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.id == speech_id,
                    TntSceneSpeech.elid == elid,
                    TntSceneSpeech.played == 1,
                    TntSceneSpeech.status == 0,
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if speech:
            # 如果找到记录，返回400错误（只能获得正确且未处理发言所对应的任务）
            return None
        else:
            # 如果没有记录，从tnt_speech_job中根据elid获取job list（id从小到大排序）
            jobs = (
                db.query(TntSpeechJob)
                .filter(
                    and_(
                        TntSpeechJob.elid == elid,
                        TntSpeechJob.tenant_id == current_user.tenant_id,
                    )
                )
                .order_by(TntSpeechJob.id.asc())
                .all()
            )

            # 构造返回结果
            jobs_response = [
                SceneSpeechJobResponse(
                    id=job.id,
                    cid=job.cid,
                    status=job.status,
                    sync=job.sync,
                    ctime=job.ctime,
                )
                for job in jobs
            ]

            return SceneSpeechJobsResponse(jobs=jobs_response)
    except Exception as e:
        logger.error(f"Error getting scene speech jobs: {e}")
        return None


def _reset_job_status(db: Session, job_id: int, tenant_id: int) -> None:
    """
    重置job状态为0，使用独立事务

    Args:
        db: 数据库会话
        job_id: 任务ID
        tenant_id: 租户ID
    """
    try:
        job_to_reset = (
            db.query(TntSpeechJob)
            .filter(
                and_(
                    TntSpeechJob.id == job_id,
                    TntSpeechJob.tenant_id == tenant_id,
                )
            )
            .first()
        )
        if job_to_reset and job_to_reset.status == 1:
            job_to_reset.status = 0
            db.commit()
    except Exception as reset_error:
        logger.error(f"Error resetting job status: {reset_error}")
        db.rollback()


async def _handle_speech_job_completion(
    db: Session, 
    job_id: int, 
    job: TntSpeechJob, 
    current_user: TntStudent, 
    full_content: str
) -> None:
    """
    处理发言任务完成回调，使用单一事务处理数据库操作

    Args:
        db: 数据库会话
        job_id: 任务ID
        job: 任务对象
        current_user: 当前用户
        full_content: AI生成的完整内容

    Raises:
        DatabaseException: 数据库操作失败
    """
    try:
        # 在单一事务中完成所有数据库操作
        # 添加新的speech记录
        new_speech = TntSceneSpeech(
            tenant_id=current_user.tenant_id,
            elid=job.elid,
            cid=job.cid,
            played=0,
            content=full_content,
            to_cids="",
            status=2,
        )
        db.add(new_speech)

        # 使用乐观锁验证job状态
        job_to_complete = (
            db.query(TntSpeechJob)
            .filter(
                and_(
                    TntSpeechJob.id == job_id,
                    TntSpeechJob.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not job_to_complete or job_to_complete.status != 1:
            raise DatabaseException("任务状态不一致", "可能存在并发修改")

        # 删除任务记录
        db.delete(job_to_complete)

        # 检查是否还有其他任务未处理
        remaining_jobs = (
            db.query(TntSpeechJob)
            .filter(
                and_(
                    TntSpeechJob.tenant_id == current_user.tenant_id,
                    TntSpeechJob.elid == job.elid,
                    TntSpeechJob.sid == job.sid,
                )
            )
            .count()
        )

        # 如果没有剩余任务，更新对应的tnt_scene_speech状态为2
        if remaining_jobs == 0:
            original_speech = (
                db.query(TntSceneSpeech)
                .filter(
                    and_(
                        TntSceneSpeech.id == job.sid,
                        TntSceneSpeech.tenant_id == current_user.tenant_id,
                    )
                )
                .first()
            )

            if original_speech:
                original_speech.status = 2

        # 提交所有更改
        db.commit()

    except Exception as e:
        logger.error(f"Error in completion callback: {e}")
        # 回调失败，回滚事务并恢复任务状态
        db.rollback()
        _reset_job_status(db, job_id, current_user.tenant_id)
        raise DatabaseException("回调函数执行", str(e))


async def process_speech_job(db: Session, job_id: int, current_user: TntStudent):
    """
    处理发言任务

    Args:
        db: 数据库会话
        job_id: 任务ID
        current_user: 当前用户

    Returns:
        StreamingResponse: 流式响应对象

    Raises:
        SpeechJobNotFoundException: 任务不存在
        SpeechJobInvalidStatusException: 任务状态无效
        ResourceNotFoundException: 相关资源不存在（包括机器人配置）
        AIServiceException: AI服务调用失败
        DatabaseException: 数据库操作失败
    """

    # Step 1: 使用悲观锁获取并更新任务状态
    job = None
    try:
        # 首先使用乐观锁检查任务是否存在
        job_check = (
            db.query(TntSpeechJob)
            .filter(
                and_(
                    TntSpeechJob.id == job_id,
                    TntSpeechJob.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not job_check:
            raise SpeechJobNotFoundException(str(job_id))

        if job_check.status != 0:
            raise SpeechJobInvalidStatusException(job_check.status)

        # 使用悲观锁获取并更新状态
        job = (
            db.query(TntSpeechJob)
            .filter(
                and_(
                    TntSpeechJob.id == job_id,
                    TntSpeechJob.status == 0,
                    TntSpeechJob.tenant_id == current_user.tenant_id,
                )
            )
            .with_for_update()  # 悲观锁，防止并发
            .first()
        )

        if not job:
            # 在悲观锁期间状态可能被其他事务修改
            raise SpeechJobInvalidStatusException(1)  # 假设已被其他进程处理

        # 原子性更新状态为1（分布式锁）
        job.status = 1
        db.commit()
    except (SpeechJobNotFoundException, SpeechJobInvalidStatusException):
        # 重新抛出业务异常
        raise
    except Exception as e:
        logger.error(f"Error acquiring job lock: {e}")
        db.rollback()
        raise DatabaseException("获取任务锁", str(e))

    # Step 2: 数据准备阶段 - 使用统一的异常处理
    try:
        # 根据任务记录中的sid（发言ID）找到tnt_scene_speech中的content
        speech = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.id == job.sid,
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                )
            )
            .first()
        )

        if not speech:
            raise ResourceNotFoundException("发言记录", str(job.sid))

        # 获取机器人配置
        bot_config = get_bot_config(db, current_user.tenant_id, BOT_KEY_ZHSPK)
        if not bot_config:
            raise ResourceNotFoundException("机器人配置", BOT_KEY_ZHSPK)

        # 解析variables
        variables = {}
        if job.vars:
            try:
                variables = json.loads(job.vars)
            except json.JSONDecodeError:
                variables = {}

    except Exception as e:
        # 统一的异常处理：恢复状态并重新抛出
        logger.error(f"Error in data preparation phase: {e}")
        _reset_job_status(db, job_id, current_user.tenant_id)

        # 根据异常类型重新抛出
        if isinstance(e, (ResourceNotFoundException, AIServiceException)):
            raise e
        else:
            raise DatabaseException("准备任务数据", str(e))

    # Step 3: 创建回调函数
    async def completion_callback(full_content: str) -> None:
        await _handle_speech_job_completion(
            db, job_id, job, current_user, full_content
        )

    # Step 4: 调用AI服务（不在事务中）
    try:
        return await get_fastgpt_stream_response(
            messages=[speech.content],
            api_endpoint=bot_config["api_endpoint"],
            api_key=bot_config["api_key"],
            variables=variables,
            on_completion=completion_callback,
        )
    except Exception as e:
        # AI服务调用失败，恢复任务状态
        logger.error(f"Error calling AI service: {e}")
        _reset_job_status(db, job_id, current_user.tenant_id)
        raise AIServiceException(str(e))


def batch_delete_scene_speeches(
    db: Session, from_id: int, elid: int, current_user: TntStudent
) -> int:
    """
    批量删除对话

    Args:
        db: 数据库会话
        from_id: 对话ID，删除ID大于等于此值的所有对话
        elid: 练习情况ID
        current_user: 当前用户

    Returns:
        删除的对话数量

    Raises:
        DatabaseException: 数据库操作失败
    """
    try:
        # 在单一事务中执行所有删除操作
        
        # 首先获取要删除的 speech ID 列表
        speech_ids_to_delete = (
            db.query(TntSceneSpeech.id)
            .filter(
                and_(
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                    TntSceneSpeech.elid == elid,
                    TntSceneSpeech.id >= from_id,
                )
            )
            .all()
        )
        
        speech_ids = [speech_id[0] for speech_id in speech_ids_to_delete]
        
        if not speech_ids:
            db.commit()
            return 0
        
        # 先删除关联的 tnt_speech_job 记录
        db.query(TntSpeechJob).filter(
            and_(
                TntSpeechJob.tenant_id == current_user.tenant_id,
                TntSpeechJob.sid.in_(speech_ids),
            )
        ).delete(synchronize_session=False)
        
        
        # 然后删除 speech 记录
        speech_deleted_count = (
            db.query(TntSceneSpeech)
            .filter(
                and_(
                    TntSceneSpeech.tenant_id == current_user.tenant_id,
                    TntSceneSpeech.elid == elid,
                    TntSceneSpeech.id >= from_id,
                )
            )
            .delete(synchronize_session=False)
        )

        db.commit()
        return speech_deleted_count
    except Exception as e:
        logger.error(f"Error batch deleting speeches: {e}")
        db.rollback()
        raise DatabaseException("批量删除对话", str(e))
